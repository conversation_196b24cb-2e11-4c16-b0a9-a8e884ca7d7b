# Required environment variables
DISCORD_APPLICATION_ID=
DISCORD_API_TOKEN= # Bot token
OPENAI_API_KEY=sk-* # OpenAI API key, starting with sk-
REDPILL_API_KEY= # REDPILL API Key
GROK_API_KEY= # GROK API Key
GROQ_API_KEY=gsk_*
OPENROUTER_API_KEY=
GOOGLE_GENERATIVE_AI_API_KEY= # Gemini API key

ELEVENLABS_XI_API_KEY= # API key from elevenlabs

# ELEVENLABS SETTINGS
ELEVENLABS_MODEL_ID=eleven_multilingual_v2
ELEVENLABS_VOICE_ID=21m00Tcm4TlvDq8ikWAM
ELEVENLABS_VOICE_STABILITY=0.5
ELEVENLABS_VOICE_SIMILARITY_BOOST=0.9
ELEVENLABS_VOICE_STYLE=0.66
ELEVENLABS_VOICE_USE_SPEAKER_BOOST=false
ELEVENLABS_OPTIMIZE_STREAMING_LATENCY=4
ELEVENLABS_OUTPUT_FORMAT=pcm_16000

TWITTER_DRY_RUN=false
TWITTER_USERNAME= # Account username
TWITTER_PASSWORD= # Account password
TWITTER_EMAIL= # Account email
TWITTER_COOKIES= # Account cookies

X_SERVER_URL=
XAI_API_KEY=
XAI_MODEL=

#POST INTERVAL RANDOM MIN-MAX MINUTES
POST_INTERVAL_MIN=  #90 #Default
POST_INTERVAL_MAX= #180 #Default


#USE IMAGE GEN
IMAGE_GEN= #TRUE

#Leave blank to use local embeddings
USE_OPENAI_EMBEDDING=  #TRUE

#OpenRouter (Use one model for everything or set individual for small, medium, large tasks)
#leave blank to use defaults hermes 70b for small tasks & 405b for medium/large tasks
OPENROUTER_MODEL=
SMALL_OPENROUTER_MODEL=
MEDIUM_OPENROUTER_MODEL=
LARGE_OPENROUTER_MODEL=


#Set to Use for New OLLAMA provider
OLLAMA_SERVER_URL=   #Leave blank for default localhost:11434
OLLAMA_MODEL=
OLLAMA_EMBEDDING_MODEL=     #default mxbai-embed-large
#To use custom model types for different tasks set these
SMALL_OLLAMA_MODEL=         #default llama3.2
MEDIUM_OLLAMA_MODEL=        #default herems3
LARGE_OLLAMA_MODEL=         #default hermes3:70b

# For asking Claude stuff
ANTHROPIC_API_KEY=

# Heurist API (Get API Key at https://heurist.ai/dev-access)
HEURIST_API_KEY=
SMALL_HEURIST_MODEL=
MEDIUM_HEURIST_MODEL=
LARGE_HEURIST_MODEL=
HEURIST_IMAGE_MODEL=

# DeepSeek Configuration
DEEPSEEK_API_KEY=
DEEPSEEK_API_URL=              # Default: https://api.deepseek.com
SMALL_DEEPSEEK_MODEL=          # Default: deepseek-chat
MEDIUM_DEEPSEEK_MODEL=         # Default: deepseek-chat
LARGE_DEEPSEEK_MODEL=          # Default: deepseek-chat

WALLET_PRIVATE_KEY=EXAMPLE_WALLET_PRIVATE_KEY
WALLET_PUBLIC_KEY=EXAMPLE_WALLET_PUBLIC_KEY

BIRDEYE_API_KEY=

SOL_ADDRESS=So11111111111111111111111111111111111111112
SLIPPAGE=1
BASE_MINT=So11111111111111111111111111111111111111112
RPC_URL=https://api.mainnet-beta.solana.com
HELIUS_API_KEY=


## Telegram
TELEGRAM_BOT_TOKEN=

TOGETHER_API_KEY=
SERVER_PORT=3000

# Starknet
STARKNET_ADDRESS=
STARKNET_PRIVATE_KEY=

# When true, disables interactive chat mode for background process operation
DAEMON_PROCESS=false
