{"name": "@elizaos/eliza-starter", "version": "0.1.9", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsup src/index.ts --format esm --dts", "start": "bun run src/index.ts", "clean": "./scripts/clean.sh", "start:service:all": "pm2 start pnpm --name=\"all\" --restart-delay=3000 --max-restarts=10 -- run start:all", "stop:service:all": "pm2 stop all"}, "dependencies": {"@elizaos/adapter-pglite": "0.25.6-alpha.1", "@elizaos/adapter-postgres": "0.25.6-alpha.1", "@elizaos/client-auto": "0.25.6-alpha.1", "@elizaos/client-direct": "0.25.6-alpha.1", "@elizaos/client-discord": "0.25.6-alpha.1", "@elizaos/client-telegram": "0.25.6-alpha.1", "@elizaos/client-twitter": "0.25.6-alpha.1", "@elizaos/core": "0.25.6-alpha.1", "@elizaos/plugin-bootstrap": "0.25.6-alpha.1", "@elizaos/plugin-coinmarketcap": "^0.25.6-alpha.1", "@elizaos/plugin-image-generation": "0.25.6-alpha.1", "@elizaos/plugin-node": "0.25.6-alpha.1", "@elizaos/plugin-solana": "0.25.6-alpha.1", "@elizaos/plugin-starknet": "0.25.6-alpha.1", "@elizaos/plugin-web-search": "^0.25.6-alpha.1", "@tavily/core": "0.0.2", "amqplib": "0.10.5", "fs": "0.0.1-security", "net": "1.0.2", "path": "0.12.7", "readline": "1.3.0", "url": "0.11.4", "ws": "8.18.0", "yargs": "17.7.2"}, "overrides": {"onnxruntime-node": "1.19.2"}, "engines": {"node": ">=22"}, "pnpm": {"overrides": {"onnxruntime-node": "1.20.0"}, "onlyBuiltDependencies": []}, "devDependencies": {"pm2": "5.4.3", "ts-node": "10.9.2", "tsup": "8.3.5", "typescript": "5.6.3"}}