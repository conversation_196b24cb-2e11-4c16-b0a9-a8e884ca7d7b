import { PostgresDatabaseAdapter } from "@elizaos/adapter-postgres";
import PgliteDatabaseAdapter from "@elizaos/adapter-pglite"; // Correction: Import par défaut
import path from "path";

export function initializeDatabase(dataDir: string) {
  // Si POSTGRES_URL est défini, utilise PostgreSQL
  if (process.env.POSTGRES_URL) {
    const db = new PostgresDatabaseAdapter({
      connectionString: process.env.POSTGRES_URL,
    });
    return db;
  } else {
    // Sinon, utilise PGLite comme base de données par défaut
    // PGLite est une implémentation légère et sans dépendance native de PostgreSQL
    const filePath = path.resolve(dataDir, "db.sqlite"); // Chemin pour le fichier de base de données PGLite
    const db = new PgliteDatabaseAdapter({
      filePath: filePath,
    });
    return db;
  }
}