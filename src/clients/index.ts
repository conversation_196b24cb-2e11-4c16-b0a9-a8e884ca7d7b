import { AutoClientInterface } from "@elizaos/client-auto";
import { DiscordClientInterface } from "@elizaos/client-discord";
import { TelegramClientInterface } from "@elizaos/client-telegram";
import { TwitterClientInterface } from "@elizaos/client-twitter";
import { elizaLogger, type Character, type IAgentRuntime, type Plugin } from "@elizaos/core";

// Définition d'une interface minimale pour s'assurer que les instances de client ont une propriété 'name' pour le logging
// et une méthode 'stop' si nécessaire pour l'interface ClientInstance plus large.
// Les ...ClientInterface.start() devraient retourner des objets conformes à ce que runtime.clients attend.
interface ClientInstance {
    name?: string;
    id?: string; // Souvent les clients ont un ID
    stop?: (runtime?: IAgentRuntime) => Promise<unknown>;
    // Ajoutez d'autres propriétés communes si connues
    [key: string]: any; // Pour permettre d'autres propriétés
}

export async function initializeClients(
  character: Character,
  runtime: IAgentRuntime
): Promise<Record<string, ClientInstance>> {
  const initializedClients: Record<string, ClientInstance> = {};
  const clientTypes = character.clients?.map((str) => str.toLowerCase().trim()) || [];

  elizaLogger.info("--- DEBUG: [initializeClients] Démarrage de l'initialisation des clients ---");
  elizaLogger.info("--- DEBUG: [initializeClients] Clients demandés dans character.clients:", JSON.stringify(clientTypes));

  if (clientTypes.includes("auto")) {
    elizaLogger.info("--- DEBUG: [initializeClients] Tentative de démarrage de AutoClientInterface...");
    try {
      const autoClient = await AutoClientInterface.start(runtime);
      if (autoClient) {
        initializedClients.auto = autoClient as ClientInstance; // Cast pour le type
        elizaLogger.success("--- DEBUG: [initializeClients] AutoClientInterface démarré avec succès.");
      } else {
        elizaLogger.warn("--- DEBUG: [initializeClients] AutoClientInterface.start() n'a rien retourné.");
      }
    } catch (error) {
      elizaLogger.error("--- DEBUG: [initializeClients] ÉCHEC du démarrage de AutoClientInterface:", error);
      throw error; // Relancer pour signaler l'échec global
    }
  }

  if (clientTypes.includes("discord")) {
    elizaLogger.info("--- DEBUG: [initializeClients] Tentative de démarrage de DiscordClientInterface...");
    try {
      const discordClient = await DiscordClientInterface.start(runtime);
      initializedClients.discord = discordClient as ClientInstance;
      elizaLogger.success("--- DEBUG: [initializeClients] DiscordClientInterface démarré avec succès.");
    } catch (error) {
      elizaLogger.error("--- DEBUG: [initializeClients] ÉCHEC du démarrage de DiscordClientInterface:", error);
      throw error;
    }
  }

  if (clientTypes.includes("telegram")) {
    elizaLogger.info("--- DEBUG: [initializeClients] Tentative de démarrage de TelegramClientInterface...");
    try {
      const telegramClient = await TelegramClientInterface.start(runtime);
      if (telegramClient) {
        initializedClients.telegram = telegramClient as ClientInstance;
        elizaLogger.success("--- DEBUG: [initializeClients] TelegramClientInterface démarré avec succès.");
      } else {
        elizaLogger.warn("--- DEBUG: [initializeClients] TelegramClientInterface.start() n'a rien retourné.");
      }
    } catch (error) {
      elizaLogger.error("--- DEBUG: [initializeClients] ÉCHEC du démarrage de TelegramClientInterface:", error);
      throw error;
    }
  }

  if (clientTypes.includes("twitter")) {
    elizaLogger.info("--- DEBUG: [initializeClients] Tentative de démarrage de TwitterClientInterface...");
    try {
      const twitterClient = await TwitterClientInterface.start(runtime);
      initializedClients.twitter = twitterClient as ClientInstance; 
      elizaLogger.success("--- DEBUG: [initializeClients] TwitterClientInterface démarré avec succès.");
    } catch (error) {
      elizaLogger.error("--- DEBUG: [initializeClients] ÉCHEC du démarrage de TwitterClientInterface:", error);
      throw error;
    }
  }

  // Cette section est pour les plugins qui pourraient exposer des clients.
  // La convention pour cela est moins standardisée que pour les services.
  if (Array.isArray(character.plugins)) {
    for (const plugin of character.plugins as Plugin[]) {
      const pluginNameForLog = (plugin as any).npmName || plugin.name || 'Plugin Inconnu';
      // Vérifier si le plugin expose une propriété 'clients' qui est un tableau de définitions de client.
      // Une définition de client ici est supposée être un objet avec une méthode `start` et un `name`.
      if (plugin && Array.isArray((plugin as any).clients)) {
        elizaLogger.info(`--- DEBUG: [initializeClients] Le plugin '${pluginNameForLog}' expose une propriété 'clients'.`);
        const pluginClientDefinitions = (plugin as any).clients as { name: string, start: (runtime: IAgentRuntime) => Promise<ClientInstance> }[];
        for (const clientDefinition of pluginClientDefinitions) {
          const clientKey = clientDefinition.name.toLowerCase().replace(/[^a-z0-9]/gi, ''); // Générer une clé sûre
          elizaLogger.info(`--- DEBUG: [initializeClients] Tentative de démarrage du client '${clientDefinition.name}' (clé: ${clientKey}) du plugin '${pluginNameForLog}'.`);
          try {
            if (typeof clientDefinition.start === 'function') {
              const clientInstance = await clientDefinition.start(runtime);
              initializedClients[clientKey] = clientInstance;
              elizaLogger.success(`--- DEBUG: [initializeClients] Client '${clientDefinition.name}' du plugin '${pluginNameForLog}' démarré avec succès.`);
            } else {
              elizaLogger.warn(`--- DEBUG: [initializeClients] La définition du client '${clientDefinition.name}' du plugin '${pluginNameForLog}' n'a pas de méthode start valide.`);
            }
          } catch (error) {
            elizaLogger.error(`--- DEBUG: [initializeClients] ÉCHEC du démarrage du client '${clientDefinition.name}' du plugin '${pluginNameForLog}':`, error);
            throw error; 
          }
        }
      }
    }
  }
  elizaLogger.info("--- DEBUG: [initializeClients] Fin de l'initialisation. Clients initialisés (clés):", Object.keys(initializedClients));
  return initializedClients;
}