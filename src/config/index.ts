import { Character, ModelProviderName, settings, validateCharacterConfig, eliza<PERSON>ogger, type Plugin } from "@elizaos/core";
import fs from "fs";
import path from "path";
import yargs from "yargs";

export function parseArguments(): {
  character?: string;
  characters?: string;
} {
  try {
    return yargs(process.argv.slice(2))
      .option("character", {
        type: "string",
        description: "Path to the character JSON file",
      })
      .option("characters", {
        type: "string",
        description: "Comma separated list of paths to character JSON files",
      })
      .parseSync();
  } catch (error) {
    console.error("Error parsing arguments:", error);
    return {};
  }
}

// Fonction pour importer dynamiquement les plugins et gérer les exports
async function handlePluginImporting(pluginNames: string[]): Promise<Plugin[]> {
  if (!pluginNames || pluginNames.length === 0) {
    return [];
  }
  elizaLogger.info("--- DEBUG: Noms des plugins à importer DANS handlePluginImporting:", pluginNames);

  const loadedPlugins: Plugin[] = [];
  for (const pluginName of pluginNames) {
    try {
      elizaLogger.info(`--- DEBUG: Tentative d'importation de ${pluginName}...`);
      const importedModule = await import(pluginName);
      elizaLogger.info(`--- DEBUG: Module ${pluginName} importé:`, Object.keys(importedModule));

      let pluginObject: Plugin | undefined;
      if (importedModule.default) {
        pluginObject = importedModule.default as Plugin;
        elizaLogger.info(`--- DEBUG: Utilisation de l'export par défaut pour ${pluginName}`);
      } else {
        // Essayer de trouver un export nommé conventionnel (ex: webSearchPlugin pour @elizaos/plugin-web-search)
        const parts = pluginName.split('/');
        const lastPart = parts[parts.length - 1].replace(/^plugin-/, '');
        const camelCaseName = lastPart.replace(/-([a-z])/g, (g) => g[1].toUpperCase()) + 'Plugin';
        if (importedModule[camelCaseName]) {
          pluginObject = importedModule[camelCaseName] as Plugin;
          elizaLogger.info(`--- DEBUG: Utilisation de l'export nommé ${camelCaseName} pour ${pluginName}`);
        }
      }

      if (pluginObject) {
        // S'assurer que l'objet a une propriété 'name' (ou 'npmName' si c'est la convention)
        // et qu'il ressemble à un objet Plugin
        if (typeof pluginObject === 'object' && pluginObject !== null && ('name' in pluginObject || 'npmName' in pluginObject)) {
          // Ajout de npmName si ce n'est pas déjà fait par le plugin lui-même
          (pluginObject as any).npmName = (pluginObject as any).npmName || pluginName;
          loadedPlugins.push(pluginObject);
          elizaLogger.info(`--- DEBUG: Plugin ${pluginName} chargé avec succès. Objet:`, JSON.stringify(pluginObject, (key, value) => typeof value === 'function' ? 'Function' : value, 2));

          // Log spécifique pour web-search
          if (pluginName === "@elizaos/plugin-web-search") {
            elizaLogger.info("--- DEBUG: Objet plugin Web Search DÉTAILLÉ APRÈS IMPORT:", JSON.stringify(pluginObject, (key, value) => typeof value === 'function' ? 'Function' : value, 2));
            elizaLogger.info("--- DEBUG: Web Search Plugin a-t-il une propriété 'services'?", 'services' in pluginObject);
            if ('services' in pluginObject && Array.isArray((pluginObject as any).services)) {
                elizaLogger.info("--- DEBUG: Contenu de la propriété 'services' du Web Search Plugin:", JSON.stringify((pluginObject as any).services, null, 2));
            } else {
                elizaLogger.warn("--- DEBUG: Propriété 'services' manquante ou malformée pour Web Search Plugin.");
            }
          }

        } else {
          elizaLogger.error(`--- DEBUG: L'exportation principale de ${pluginName} ne ressemble pas à un objet Plugin valide.`);
        }
      } else {
        elizaLogger.error(`--- DEBUG: Impossible de trouver l'exportation principale (default ou nommée) pour le plugin ${pluginName}.`);
      }
    } catch (e) {
      elizaLogger.error(`--- DEBUG: Erreur lors de l'importation dynamique du plugin ${pluginName}:`, e);
    }
  }
  elizaLogger.info("--- DEBUG: Tableau final des plugins chargés DANS handlePluginImporting:", loadedPlugins.map(p=>(p as any).npmName || (p as any).name));
  return loadedPlugins;
}


export async function loadCharacters(
  charactersArg: string
): Promise<Character[]> {
  let characterPaths = charactersArg?.split(",").map((filePath) => {
    if (path.basename(filePath) === filePath) {
      // Si seulement un nom de fichier est donné, chercher dans le dossier par défaut '../characters/'
      filePath = "../characters/" + filePath;
    }
    return path.resolve(process.cwd(), filePath.trim());
  });

  const loadedCharacters: Character[] = [];

  if (characterPaths?.length > 0) {
    for (const charPath of characterPaths) { // Renommé path en charPath pour éviter confusion avec le module path
      try {
        const characterData = JSON.parse(fs.readFileSync(charPath, "utf8"));
        validateCharacterConfig(characterData); // Valide la structure de base

        // --- MODIFICATION CRUCIALE ICI ---
        if (Array.isArray(characterData.plugins) && characterData.plugins.every((p: any) => typeof p === 'string')) {
          elizaLogger.info(`--- DEBUG: Transformation des plugins pour ${characterData.name}. Plugins avant:`, JSON.stringify(characterData.plugins));
          characterData.plugins = await handlePluginImporting(characterData.plugins as string[]);
          elizaLogger.info(`--- DEBUG: Plugins pour ${characterData.name} APRÈS transformation (devraient être des objets):`, JSON.stringify(characterData.plugins.map((p: any)=>(p as any).npmName || (p as any).name), null, 2));
        } else {
          elizaLogger.warn(`--- DEBUG: characterData.plugins pour ${characterData.name} n'est pas un tableau de chaînes ou est déjà transformé/vide.`);
          characterData.plugins = characterData.plugins || []; // S'assurer que c'est un tableau
        }
        // --- FIN DE LA MODIFICATION CRUCIALE ---

        loadedCharacters.push(characterData as Character);
      } catch (e) {
        elizaLogger.error(`Error loading character from ${charPath}:`, e);
        process.exit(1);
      }
    }
  }

  return loadedCharacters;
}

export function getTokenForProvider(
  provider: ModelProviderName | string | undefined, // Rendre provider optionnel et accepter string
  character: Character
) {
  // Gérer le cas où provider est undefined ou une chaîne non reconnue
  const modelProvider = provider as ModelProviderName; // Cast pour le switch, mais attention si c'est une chaîne inconnue

  switch (modelProvider) {
    case ModelProviderName.OPENAI:
      return (
        character.settings?.secrets?.OPENAI_API_KEY || settings.OPENAI_API_KEY
      );
    case ModelProviderName.LLAMACLOUD: // Ce cas semble combiner plusieurs fournisseurs
      return (
        character.settings?.secrets?.LLAMACLOUD_API_KEY ||
        settings.LLAMACLOUD_API_KEY ||
        character.settings?.secrets?.TOGETHER_API_KEY ||
        settings.TOGETHER_API_KEY ||
        character.settings?.secrets?.XAI_API_KEY ||
        settings.XAI_API_KEY ||
        character.settings?.secrets?.OPENAI_API_KEY || // Fallback sur OPENAI ?
        settings.OPENAI_API_KEY
      );
    case ModelProviderName.ANTHROPIC:
      return (
        character.settings?.secrets?.ANTHROPIC_API_KEY ||
        character.settings?.secrets?.CLAUDE_API_KEY ||
        settings.ANTHROPIC_API_KEY ||
        settings.CLAUDE_API_KEY
      );
    case ModelProviderName.REDPILL: // N'est pas dans l'enum ModelProviderName de @elizaos/core
      return (
        character.settings?.secrets?.REDPILL_API_KEY || settings.REDPILL_API_KEY
      );
    case ModelProviderName.OPENROUTER:
      return (
        character.settings?.secrets?.OPENROUTER_API_KEY || // Corrigé pour correspondre à .env
        character.settings?.secrets?.OPENROUTER || // Ancien nom possible
        settings.OPENROUTER_API_KEY
      );
    case ModelProviderName.GROK: // N'est pas dans l'enum ModelProviderName de @elizaos/core
      return character.settings?.secrets?.GROK_API_KEY || settings.GROK_API_KEY;
    case ModelProviderName.HEURIST: // N'est pas dans l'enum ModelProviderName de @elizaos/core
      return (
        character.settings?.secrets?.HEURIST_API_KEY || settings.HEURIST_API_KEY
      );
    case ModelProviderName.GROQ:
      return character.settings?.secrets?.GROQ_API_KEY || settings.GROQ_API_KEY;
    default:
      elizaLogger.warn(`getTokenForProvider: ModelProvider inconnu ou non géré: ${provider}`);
      return undefined; // Ou une valeur par défaut si approprié
  }
}