import { DirectClient } from "@elizaos/client-direct";
import {
  AgentRuntime,
  elizaLogger,
  settings,
  stringToUuid,
  type Character,
  type IAgentRuntime,
  type Memory,
  type UUID,
  type Plugin,
  type Service, // Import de Service pour typer PatchedWebSearchService
} from "@elizaos/core";
import { bootstrapPlugin } from "@elizaos/plugin-bootstrap";
import { createNodePlugin } from "@elizaos/plugin-node";
import { solanaPlugin } from "@elizaos/plugin-solana";
import fs from "fs";
import net from "net";
import path from "path";
import { fileURLToPath } from "url";
import { initializeDbCache } from "./cache/index.ts";
import { character } from "./character.ts";
import { startChat } from "./chat/index.ts";
import { initializeClients } from "./clients/index.ts";
import {
  getTokenForProvider,
  loadCharacters,
  parseArguments,
} from "./config/index.ts";
import { initializeDatabase } from "./database/index.ts";

// --- DÉFINITIONS LOCALES DES ENUMS MODELTYPE ---
export enum ModelType {
  TEXT_SMALL = 'text_small',
  TEXT_LARGE = 'text_large',
  TEXT_EMBEDDING = 'text_embedding',
  TEXT_TOKENIZER_ENCODE = 'text_tokenizer_encode',
  TEXT_TOKENIZER_DECODE = 'text_tokenizer_decode',
  TEXT_REASONING_SMALL = 'text_reasonsing_small',
  TEXT_REASONING_LARGE = 'text_reasonsing_large',
  IMAGE = 'image',
  IMAGE_DESCRIPTION = 'image_description',
  TRANSCRIPTION = 'transcription',
  TEXT_TO_SPEECH = 'text_to_speech',
  AUDIO = 'audio',
  VIDEO = 'video',
  OBJECT_SMALL = 'object_small',
  OBJECT_LARGE = 'object_large',
}
// --- FIN DES DÉFINITIONS LOCALES ---

interface ElizaTwitterClientInstance {
  scraper: {
    getTweets: (username: string, count?: number) => Promise<any[]>;
  };
  sendQuoteTweet: (comment: string, tweetId: string) => Promise<any>;
}

interface ElizaWebSearchServiceInstance {
  search: (query: string, options?: { count?: number }) => Promise<any>;
  initialize?: (runtime: IAgentRuntime) => Promise<void>;
}

interface ElizaCoinMarketCapServiceInstance {
  getPrice: (symbol: string, currency: string) => Promise<any>;
}

// --- MODIFICATION ICI : Import de l'objet Plugin complet et extraction de la classe réelle ---
import WebSearchPluginObject from "@elizaos/plugin-web-search";

// Accéder à la classe WebSearchService réelle via la propriété 'services' de l'objet Plugin
const serviceInstanceFromPlugin = WebSearchPluginObject.services?.[0];
let WebSearchServiceRawClass: any = undefined;

if (serviceInstanceFromPlugin && typeof serviceInstanceFromPlugin === 'object') {
    WebSearchServiceRawClass = serviceInstanceFromPlugin.constructor;
    if (typeof WebSearchServiceRawClass !== 'function') {
        elizaLogger.error("FATAL: OriginalWebSearchServiceClass récupéré n'est pas un constructeur !");
        WebSearchServiceRawClass = undefined;
    }
} else {
    elizaLogger.error("FATAL: Impossible de récupérer l'instance de service depuis le plugin WebSearch.");
}

// Vérification si la classe a été trouvée (pour arrêter tôt si nécessaire)
if (!WebSearchServiceRawClass || typeof WebSearchServiceRawClass !== 'function') {
    elizaLogger.error("FATAL: Impossible de trouver la classe WebSearchService réelle dans le plugin. Vérifiez la structure du plugin.");
    // Si cette classe est introuvable, le service ne pourra pas être initialisé.
    // On peut choisir d'arrêter l'application ici si c'est critique.
    // process.exit(1);
}
// --- FIN MODIFICATION ---


// --- DÉBUT DE LA NOUVELLE CLASSE DE SERVICE PATCHÉE ---
// Cette classe implémente le contrat 'Service' attendu par AgentRuntime
class PatchedWebSearchService implements Service { // Implémente Service directement
    public readonly serviceType: string = 'web_search'; // Propriété d'instance
    private runtime: IAgentRuntime;
    private originalWebSearchServiceInstance: any; // Instance du service original

    constructor(runtime: IAgentRuntime) {
        this.runtime = runtime;
        // Instancier le service original si la classe est disponible
        if (WebSearchServiceRawClass) {
            try {
                this.originalWebSearchServiceInstance = new WebSearchServiceRawClass();
                elizaLogger.info(`[PatchedWebSearchService] Instance de WebSearchServiceRawClass créée.`);
            } catch (e) {
                elizaLogger.error(`[PatchedWebSearchService] Erreur lors de l'instanciation de WebSearchServiceRawClass:`, e);
            }
        } else {
            elizaLogger.warn(`[PatchedWebSearchService] WebSearchServiceRawClass est undefined, le service de recherche ne fonctionnera pas.`);
        }
    }

    async initialize(runtime_param: IAgentRuntime): Promise<void> {
        elizaLogger.info(`[PatchedWebSearchService] Initializing '${this.serviceType}'.`);
        // Appeler la méthode initialize de l'instance réelle du service si elle existe
        if (this.originalWebSearchServiceInstance && typeof this.originalWebSearchServiceInstance.initialize === 'function') {
            await this.originalWebSearchServiceInstance.initialize(runtime_param);
        } else {
            elizaLogger.warn(`[PatchedWebSearchService] L'instance originale de WebSearchService n'a pas de méthode initialize ou est inexistante.`);
        }
    }

    async search(query: string, options?: { count?: number }): Promise<any> {
        if (!this.originalWebSearchServiceInstance) {
            elizaLogger.error(`[PatchedWebSearchService] search: L'instance originale de WebSearchService n'est pas disponible.`);
            throw new Error("Web search service not initialized.");
        }
        return this.originalWebSearchServiceInstance.search(query, options);
    }

    // Si l'interface Service définit start/stop comme obligatoires, il faut les implémenter
    async start(): Promise<void> {
        elizaLogger.info(`[PatchedWebSearchService] Starting '${this.serviceType}'.`);
        if (this.originalWebSearchServiceInstance && typeof this.originalWebSearchServiceInstance.start === 'function') {
            await this.originalWebSearchServiceInstance.start();
        }
    }

    async stop(): Promise<void> {
        elizaLogger.info(`[PatchedWebSearchService] Stopping '${this.serviceType}'.`);
        if (this.originalWebSearchServiceInstance && typeof this.originalWebSearchServiceInstance.stop === 'function') {
            await this.originalWebSearchServiceInstance.stop();
        }
    }
}
// --- FIN DE LA NOUVELLE CLASSE DE SERVICE PATCHÉE ---


const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const wait = (minTime: number = 1000, maxTime: number = 3000) => {
  const waitTime =
    Math.floor(Math.random() * (maxTime - minTime + 1)) + minTime;
  return new Promise((resolve) => setTimeout(resolve, waitTime));
};

let nodePlugin: any | undefined;

export function createAgent(
  character: Character,
  db: any,
  cache: any,
  token: string
) {
  elizaLogger.info(
    "--- DEBUG: Entrée dans createAgent ---",
    `Pour le personnage: ${character.name}`
  );
  elizaLogger.info(
    "--- DEBUG: Contenu de character.plugins AVANT fusion DANS createAgent ---",
    JSON.stringify(character.plugins, null, 2)
  );

  nodePlugin ??= createNodePlugin();

  const characterPlugins = Array.isArray(character.plugins) ? character.plugins : [];

  elizaLogger.info(
    "--- DEBUG: characterPlugins (après Array.isArray) DANS createAgent ---",
    JSON.stringify(characterPlugins, null, 2)
  );
  characterPlugins.forEach((plugin: any) => {
    elizaLogger.info(`--- DEBUG: Plugin dans characterPlugins DANS createAgent: NPM Name: ${plugin.npmName || 'N/A'}, Name: ${plugin.name || 'N/A'}, Keys: ${Object.keys(plugin).join(', ')}`);
    if ((plugin.npmName || plugin.name) === '@elizaos/plugin-web-search') {
      elizaLogger.info("--- DEBUG: Objet plugin Web Search trouvé dans characterPlugins DANS createAgent:", JSON.stringify(plugin, null, 2));
      elizaLogger.info("--- DEBUG: Web Search Plugin a la propriété 'services'?", 'services' in plugin);
      if ('services' in plugin && plugin.services) {
        elizaLogger.info("--- DEBUG: Contenu de la propriété 'services' du Web Search Plugin:", JSON.stringify((plugin as any).services, null, 2));
      }
    }
  });

  const allLoadedPlugins = [
    bootstrapPlugin,
    nodePlugin,
    character.settings?.secrets?.WALLET_PUBLIC_KEY ? solanaPlugin : null,
    ...characterPlugins,
  ].filter(Boolean) as Plugin[];

  elizaLogger.info(
    "--- DEBUG: allLoadedPlugins (après fusion) PASSÉS à AgentRuntime ---",
    JSON.stringify(allLoadedPlugins.map(p => (p as any).name || (p as any).npmName || 'Plugin Inconnu'), null, 2)
  );

  return new AgentRuntime({
    databaseAdapter: db,
    token,
    modelProvider: character.modelProvider,
    evaluators: [],
    character,
    plugins: allLoadedPlugins,
    providers: [],
    actions: [],
    // --- MODIFICATION ICI : PASSER LA CLASSE PATCHÉE AU TABLEAU SERVICES ---
    // AgentRuntime devrait instancier et initialiser les services s'ils sont passés comme classes.
    services: [PatchedWebSearchService], // Passer la CLASSE PatchedWebSearchService
    managers: [],
    cacheManager: cache,
  });
}

const PROCESSED_TWEET_IDS = new Set<string>();

async function retweetRelevantTweets(runtime: IAgentRuntime) {
  elizaLogger.info("Starting relevant tweet check...");

  const accountsToMonitor = process.env.TWITTER_ACCOUNTS_TO_MONITOR?.split(',')
    .map(account => account.trim())
    .filter(Boolean) || [];

  if (accountsToMonitor.length === 0) {
    elizaLogger.warn("No Twitter accounts to monitor configured in TWITTER_ACCOUNTS_TO_MONITOR.");
    return;
  }

  const webSearchService = runtime.getService('web_search' as any) as unknown as ElizaWebSearchServiceInstance;

  if (!webSearchService) {
    elizaLogger.error("Web Search service is not available. Ensure it's correctly patched and initialized.");
    return;
  }

  const twitterClientInstance = runtime.clients.twitter as unknown as ElizaTwitterClientInstance;
  if (!twitterClientInstance || !twitterClientInstance.scraper || !twitterClientInstance.sendQuoteTweet) {
      elizaLogger.error("Twitter client is not fully available or its methods are not recognized. Ensure 'twitter' is in cryptox.json clients and the Twitter plugin is installed correctly.");
      return;
  }


  for (const account of accountsToMonitor) {
    try {
      elizaLogger.debug(`Fetching tweets for @${account}...`);
      const tweets = await twitterClientInstance.scraper.getTweets(account);

      for (const tweet of tweets) {
        if (PROCESSED_TWEET_IDS.has(tweet.id)) {
          elizaLogger.debug(`Tweet ${tweet.id} from @${account} already processed, skipping.`);
          continue;
        }

        const tweetTextLower = tweet.text.toLowerCase();
        const relevantTopics = runtime.character.topics?.map(t => t.toLowerCase()) || [];
        const isRelevant = relevantTopics.some(topic => tweetTextLower.includes(topic));

        if (isRelevant) {
          elizaLogger.info(`Relevant tweet found from @${account}: ${tweet.text.substring(0, 100)}...`);

          let additionalInfo = "";

          try {
            const webSearchResults = await webSearchService.search(`recent news about ${tweet.text.substring(0, 50)} cryptocurrency`, { count: 3 });
            if (webSearchResults && webSearchResults.results && webSearchResults.results.length > 0) {
              additionalInfo += "Here's some recent news: ";
              additionalInfo += webSearchResults.results.map(r => `${r.title} (${r.url})`).join("; ") + ".";
            }
          } catch (webSearchError) {
            elizaLogger.warn(`Failed to perform web search: ${webSearchError}`);
          }

          const prompt = `
            You are CryptoxFR, a passionate, sarcastic, and independent French crypto analyst. You closely follow innovative Web3 and AI projects. You like to decode buzz, expose inconsistencies, and identify real trends.
            Your posting style should be: ${runtime.character.style?.post?.join(" ")}.
            The text must be plain text, with no JSON, HTML formatting, or special characters like '|' or '<br>'.

            Here is the original tweet to comment on: "${tweet.text}"
            Tweet author: @${account}

            Additional information (recent news):
            ${additionalInfo}

            Your comment should be a concise and relevant analysis of the original tweet, integrating the additional information if pertinent.
            Your goal is to make people think, not to criticize them.
            Start your comment directly, without greeting or introduction.
            `;

          try {
            elizaLogger.debug(`Generating comment for tweet ${tweet.id}...`);

            const agentRuntimeInstance = runtime as any;
            if (typeof agentRuntimeInstance.useModel !== 'function') {
              elizaLogger.error("FATAL: runtime.useModel is not a function on the current AgentRuntime instance.");
              throw new Error("runtime.useModel is not available");
            }
            const generatedComment = await agentRuntimeInstance.useModel(ModelType.TEXT_LARGE, {
              prompt: prompt,
              temperature: 0.7,
              maxTokens: 200,
            });

            elizaLogger.info(`Generated comment for ${tweet.id}: ${generatedComment}`);

            await twitterClientInstance.sendQuoteTweet(generatedComment, tweet.id);
            elizaLogger.success(`Successfully quote-tweeted ${tweet.id}.`);
            PROCESSED_TWEET_IDS.add(tweet.id);
          } catch (llmError) {
            elizaLogger.error(`Error generating comment or publishing retweet for ${tweet.id}: ${llmError}`);
          }
        } else {
          PROCESSED_TWEET_IDS.add(tweet.id);
        }
      }
    } catch (error) {
      elizaLogger.error(`Error fetching tweets for @${account}: ${error}`);
    }
  }
  elizaLogger.info("Finished relevant tweet check.");
}


async function startAgent(character: Character, directClient: DirectClient) {
  elizaLogger.info(`--- DEBUG: [startAgent] Début de la fonction startAgent pour ${character.name} ---`);
  try {
    character.id ??= stringToUuid(character.name);
    character.username ??= character.name;

    const token = getTokenForProvider(character.modelProvider, character);
    const dataDir = path.join(__dirname, "../data");

    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const db = initializeDatabase(dataDir);

    await db.init();

    const cache = initializeDbCache(character, db);
    // IMPORTANT: Le runtime est créé ici, et PatchedWebSearchService a besoin de lui.
    // C'est pourquoi PatchedWebSearchService est instancié DANS createAgent.
    const runtime = createAgent(character, db, cache, token);

    await runtime.initialize();

    elizaLogger.info("--- DEBUG: Après runtime.initialize() DANS startAgent ---");
    if (runtime.services && typeof (runtime.services as any).has === 'function') {
        elizaLogger.info("--- DEBUG: runtime.services est une Map ou similaire. Contenu:", JSON.stringify(Array.from((runtime.services as any).entries()), null, 2));
        elizaLogger.info("--- DEBUG: Le runtime a-t-il le service 'web_search'?", (runtime.services as any).has('web_search'));
        if ((runtime.services as any).has('web_search')) {
            elizaLogger.info("--- DEBUG: Objet service 'web_search':", JSON.stringify((runtime.services as any).get('web_search'), null, 2));
        }
    } else {
        elizaLogger.warn("--- DEBUG: runtime.services n'est pas une Map ou n'a pas de méthode 'has'. runtime.services:", runtime.services);
    }
    try {
        const service = runtime.getService('web_search' as any);
        elizaLogger.info("--- DEBUG: Récupération réussie du service 'web_search' post-initialisation:", service ? 'Objet service trouvé' : 'Service non trouvé (null/undefined)');
    } catch (e) {
        elizaLogger.error("--- DEBUG: Erreur lors de l'appel à getService('web_search') post-initialisation:", (e as Error).message);
    }

    runtime.clients = await initializeClients(character, runtime);

    directClient.registerAgent(runtime);

    elizaLogger.debug(`Started ${character.name} as ${runtime.agentId}`);

    return runtime;
  } catch (error) {
    elizaLogger.error(
      `Error starting agent for character ${character.name}:`,
      error,
    );
    console.error(error);
    throw error;
  }
}

const checkPortAvailable = (port: number): Promise<boolean> => {
  return new Promise((resolve) => {
    const server = net.createServer();

    server.once("error", (err: NodeJS.ErrnoException) => {
      if (err.code === "EADDRINUSE") {
        resolve(false);
      }
    });

    server.once("listening", () => {
      server.close();
      resolve(true);
    });

    server.listen(port);
  });
};

const startAgents = async () => {
  elizaLogger.info("--- DEBUG: [startAgents] Début de la fonction startAgents ---");
  const directClient = new DirectClient();
  let serverPort = parseInt(settings.SERVER_PORT || "3000");
  const args = parseArguments();

  let charactersArg = args.characters || args.character;
  let characters = [character];

  elizaLogger.info("--- DEBUG: Avant loadCharacters DANS startAgents ---");
  console.log("--- DEBUG: charactersArg avant loadCharacters:", charactersArg);

  if (charactersArg) {
    characters = await loadCharacters(charactersArg);
  }
  elizaLogger.info("--- DEBUG: Après loadCharacters DANS startAgents ---");
  characters.forEach(char => {
    elizaLogger.info(`--- DEBUG: Personnage chargé: ${char.name}, Plugins: ${JSON.stringify(char.plugins, null, 2)}`);
  });


  try {
    for (const char of characters) {
      const agentRuntime = await startAgent(char, directClient as DirectClient);

      const isDaemonProcess = process.env.DAEMON_PROCESS === "true";
      if (isDaemonProcess && char.name === "CryptoxFR") {
        const intervalMinutes = parseInt(process.env.RETWEET_CHECK_INTERVAL_MINUTES || "30");
        elizaLogger.info(`Starting Twitter monitoring for CryptoxFR every ${intervalMinutes} minutes.`);
        await retweetRelevantTweets(agentRuntime);
        setInterval(() => retweetRelevantTweets(agentRuntime), intervalMinutes * 60 * 1000);
      }
    }
  } catch (error) {
    elizaLogger.error("Error starting agents:", error);
    elizaLogger.fatal(`--- DEBUG: [startAgents] Appel de process.exit(1) après erreur de démarrage des agents.`);
    process.exit(1);
  }

  while (!(await checkPortAvailable(serverPort))) {
    elizaLogger.warn(`Port ${serverPort} is in use, trying ${serverPort + 1}`);
    serverPort++;
  }

  directClient.startAgent = async (character: Character) => {
    return startAgent(character, directClient);
  };

  directClient.start(serverPort);

  if (serverPort !== parseInt(settings.SERVER_PORT || "3000")) {
    elizaLogger.log(`Server started on alternate port ${serverPort}`);
  }

  const isDaemonProcess = process.env.DAEMON_PROCESS === "true";
  if(!isDaemonProcess) {
    elizaLogger.log("Chat started. Type 'exit' to quit.");
    const chat = startChat(characters);
    chat();
  }
};

startAgents().catch((error) => {
  elizaLogger.error("Unhandled error in startAgents:", error);
  elizaLogger.fatal(`--- DEBUG: [startAgents] Appel de process.exit(1) dans le catch global.`);
  process.exit(1);
});